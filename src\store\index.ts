import { configureStore } from '@reduxjs/toolkit';
import { apiSlice } from './api/apiSlice';
import { nurseApiSlice } from './api/nurseApiSlice';

export const store = configureStore({
  reducer: {
    [apiSlice.reducerPath]: apiSlice.reducer,
    [nurseApiSlice.reducerPath]: nurseApiSlice.reducer,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware()
      .concat(apiSlice.middleware)
      .concat(nurseApiSlice.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
