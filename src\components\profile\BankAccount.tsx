import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const BankAccount = () => {
  const navigate = useNavigate();

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src='/Images/bg4.png'
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1 '>
          <button onClick={() => navigate(-1)} className='mr-3'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Bank Accounts</h1>
        </div>
      </header>

      {}
      <main className='flex-1 p-4'>
        <div className='md:max-w-md mx-auto'>
          <p className='text-gray-600 text-center'>
            Bank Accounts Page coming soon...
          </p>
        </div>
      </main>
    </div>
  );
};

export default BankAccount;
