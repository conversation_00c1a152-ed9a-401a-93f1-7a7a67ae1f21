export interface CustomerRegistrationData {
  email: string;
  password: string;
  given_name: string;
  middle_name?: string;
  family_name: string;
  phone_number: string;
  username: string;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  tokens?: {
    accessToken: string;
    idToken?: string;
    refreshToken?: string;
  };
  user?: {
    id: string;
    email: string;
    given_name: string;
    middle_name?: string;
    family_name: string;
    username: string;
  };
}
