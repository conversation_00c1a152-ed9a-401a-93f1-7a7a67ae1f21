import { useEffect, useState, useCallback } from 'react';
import { CheckCircle } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useUpdateBookingStatusMutation } from '../store/api/apiSlice';
import { useNavigationState } from '@/hooks/useNavigationState';
import { showErrorToast, handleApiError } from '@/utils/toast';

const CancellationSuccess = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const booking = location.state?.booking;
  const cancellationReason = location.state?.cancellationReason;
  const statusAlreadyUpdated = location.state?.statusAlreadyUpdated;
  const [updateBookingStatus] = useUpdateBookingStatusMutation();
  const [statusUpdated, setStatusUpdated] = useState(
    statusAlreadyUpdated || false
  );
  const { navigationState } = useNavigationState();

  const getErrorMessage = useCallback(
    (statusCode: number, errorMessage: string): string => {
      const errorMappings = {
        400: {
          'Cannot cancel a completed booking':
            'This booking has already been completed and cannot be cancelled.',
          'Cannot cancel a cancelled booking':
            'This booking has already been cancelled.',
          'Invalid cancellation reason ID':
            'Invalid cancellation reason. Please try again.',
          default: errorMessage || 'Invalid request. Please try again.',
        },
        403: {
          'You can only modify your own bookings':
            'You can only cancel your own bookings.',
          'Customers can only cancel bookings':
            'You can only cancel bookings, not modify their status.',
          default: 'Access denied. Please check your permissions.',
        },
        404: {
          default: 'Booking not found. It may have been deleted.',
        },
      };

      const statusMappings =
        errorMappings[statusCode as keyof typeof errorMappings];
      if (!statusMappings)
        return errorMessage || 'An unexpected error occurred.';

      for (const [key, value] of Object.entries(statusMappings)) {
        if (key !== 'default' && errorMessage?.includes(key)) {
          return value;
        }
      }
      return statusMappings.default;
    },
    []
  );

  const handleUpdateError = useCallback(
    (error: unknown) => {
      console.error('Failed to update booking status:', error);

      const apiError = error as {
        data?: { error?: string };
        message?: string;
        status?: number;
      };
      const errorMessage = apiError?.data?.error || apiError?.message || '';
      const statusCode = apiError?.status;

      if (statusCode && [400, 403, 404].includes(statusCode)) {
        const message = getErrorMessage(statusCode, errorMessage);
        showErrorToast(message);
      } else {
        handleApiError(error, {
          [statusCode || 'unknown']:
            errorMessage ||
            'Failed to update booking status. Please try again.',
        });
      }
    },
    [getErrorMessage]
  );

  useEffect(() => {
    const updateStatus = async () => {
      if (!booking?.booking_id || statusUpdated) return;
      try {
        await updateBookingStatus({
          booking_id: booking.booking_id,
          booking_status: 'Cancelled',
          ...(location.state?.cancellationReasonId && {
            cancellation_reason_id: location.state.cancellationReasonId,
          }),
        }).unwrap();
        setStatusUpdated(true);
      } catch (error: unknown) {
        handleUpdateError(error);
      }
    };

    updateStatus();
  }, [
    booking,
    updateBookingStatus,
    statusUpdated,
    location.state,
    handleUpdateError,
  ]);

  const handleClose = () => {
    navigate('/home', { state: navigationState });
  };

  if (!booking) {
    return (
      <div className='min-h-screen bg-white flex items-center justify-center'>
        <div className='text-center'>
          <p className='text-gray-600 mb-4'>No booking data found</p>
          <button
            onClick={() => navigate('/schedule')}
            className='bg-nursery-blue text-white px-4 py-2 rounded-lg'
          >
            Go to Schedule
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-white flex flex-col items-center justify-center text-center p-5'>
      <div className='flex flex-col items-center max-w-md w-full'>
        {}
        <div className='bg-green-500 rounded-full p-4 mb-4'>
          <CheckCircle className='h-10 w-10 text-white' />
        </div>

        {}
        <h2 className='text-xl font-bold text-center mb-4 text-nursery-darkBlue'>
          Confirmation
        </h2>

        {}
        <p className='text-center text-gray-700 mb-6 leading-relaxed'>
          Your booking has been cancelled successfully
        </p>

        {}
        <div className='w-full rounded-lg p-4 mb-6 bg-[#F2F2F2] border-gray-300 text-start'>
          <h4 className='text-md font-semibold text-nursery-darkBlue mb-3'>
            Reason for cancellation
          </h4>
          <p className='text-sm text-nursery-darkBlue leading-relaxed text-left'>
            {cancellationReason}
          </p>
        </div>

        {}
        <button
          onClick={handleClose}
          className='w-full h-10 px-4 py-2 items-center font-semibold border-1 shadow-lg text-white rounded-lg bg-nursery-blue hover:bg-nursery-darkBlue transition-colors duration-300'
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default CancellationSuccess;
