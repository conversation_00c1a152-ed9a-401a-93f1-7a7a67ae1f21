import React from 'react';
import { ChevronDown } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { SerializedError } from '@reduxjs/toolkit';
import ResponsiveLoader from '@/components/Loader';
import UpcomingSchedule from '@/components/UpcomingSchedule';
import { Booking, GetBookingsByCustomerResponse } from '@/store/api/apiSlice';

interface BookingsSectionProps {
  bookingResponse: GetBookingsByCustomerResponse | null;
  bookingLoading: boolean;
  bookingError: FetchBaseQueryError | SerializedError | null;
}

const BookingsSection: React.FC<BookingsSectionProps> = ({
  bookingResponse,
  bookingLoading,
  bookingError,
}) => {
  const navigate = useNavigate();

  const getFilteredBookings = (): Booking[] => {
    if (!bookingResponse?.bookings) return [];

    return bookingResponse.bookings.filter((b: Booking) => {
      const statusMatch =
        b.booking_status === 'Accepted' || b.booking_status === 'Pending';

      // For pending bookings, only show today and future dates
      if (b.booking_status === 'Pending' && statusMatch) {
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Set to start of today
        const bookingDate = new Date(b.booked_date);
        bookingDate.setHours(0, 0, 0, 0); // Set to start of booking date

        return bookingDate >= today;
      }

      return statusMatch;
    });
  };

  const getSortedBookings = (): Booking[] => {
    const filteredBookings = getFilteredBookings();
    return [...filteredBookings].sort(
      (a, b) =>
        new Date(a.booked_date).getTime() - new Date(b.booked_date).getTime()
    );
  };

  const renderBookingContent = () => {
    if (bookingLoading) {
      return (
        <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <ResponsiveLoader />
          <p className='text-base font-medium text-gray-500'>
            Loading your Bookings...
          </p>
        </div>
      );
    }

    if (bookingError) {
      return (
        <div className='bg-red-50 p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <p className='text-base font-medium text-red-500'>
            Failed to load Bookings
          </p>
          <p className='text-sm text-red-400'>Please try again later</p>
        </div>
      );
    }

    const sortedBookings = getSortedBookings();

    if (sortedBookings.length === 0) {
      return (
        <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <img
            src='/Images/calender.svg'
            alt='NO Bookings for you'
            className='p-2 w-20'
          />
          <p className='text-base font-medium text-slate-500'>
            No upcoming Bookings for you
          </p>
        </div>
      );
    }

    const latestBooking = sortedBookings[0];
    return (
      <div className='bg-[#F2F2F2] rounded-xl shadow-sm'>
        <UpcomingSchedule key={latestBooking.id} booking={latestBooking} />
      </div>
    );
  };

  const filteredBookings = getFilteredBookings();
  const hasBookings = filteredBookings.length > 0;

  return (
    <div className='md:pt-5 pt-10 max-w-full'>
      <div className='flex flex-1 justify-between items-center mb-3'>
        <h3 className='text-xl font-semibold text-gray-800'>
          Upcoming Bookings
        </h3>
        {hasBookings && (
          <button
            className='flex items-center gap-2 bg-nursery-blue text-white px-3 py-1 rounded-full shadow-xl'
            onClick={() => navigate('/schedule')}
          >
            <span className='text-white text-sm font-semibold'>View All</span>
            <ChevronDown className='w-4 h-4 rotate-[-90deg]' />
          </button>
        )}
      </div>
      {renderBookingContent()}
    </div>
  );
};

export default BookingsSection;
