import { useGetProfileDetailsQuery } from '@/store/api/apiSlice';
import {
  createNavigationStateFromParams,
  createNavigationStateWithFallback,
  NavigationState,
} from '@/utils/navigationState';

export const useNavigationState = (fallbackData?: {
  username?: string;
  givenName?: string;
  address?: string;
  userId?: string;
}) => {
  const { data: profileDetails, isLoading: profileLoading } =
    useGetProfileDetailsQuery();

  const navigationState = createNavigationStateWithFallback(
    profileDetails,
    fallbackData
  );

  return {
    navigationState,
    isLoading: profileLoading,
    profileDetails,
  };
};

export const useNavigationStateFromParams = (
  username: string,
  givenName: string,
  address: string,
  userId: string
): NavigationState => {
  return createNavigationStateFromParams(username, givenName, address, userId);
};
