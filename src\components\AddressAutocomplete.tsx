import React, { useEffect, useRef, useState } from 'react';
import { Input } from '@/components/ui/input';
import { showErrorToast } from '@/utils/toast';

interface AddressAutocompleteProps {
  onAddressSelected: (address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    formattedAddress: string;
  }) => void;
  placeholder?: string;
}

const AddressAutocomplete = ({
  onAddressSelected,
  placeholder = 'Search for your location',
}: AddressAutocompleteProps) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [apiKey, setApiKey] = useState<string>('');

  useEffect(() => {
    if (window.google?.maps?.places) {
      setIsApiLoaded(true);
      return;
    }

    if (apiKey) {
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
      script.async = true;
      script.defer = true;
      script.onload = () => setIsApiLoaded(true);
      script.onerror = () => {
        showErrorToast(
          'Failed to load Google Maps API. Please check your API key and try again.'
        );
      };
      document.head.appendChild(script);

      return () => {
        document.head.removeChild(script);
      };
    }
  }, [apiKey]);

  useEffect(() => {
    if (!isApiLoaded || !inputRef.current) return;

    let listener: google.maps.MapsEventListener | null = null;

    try {
      autocompleteRef.current = new google.maps.places.Autocomplete(
        inputRef.current,
        {
          types: ['address'],
          componentRestrictions: { country: 'us' },
          fields: ['address_component', 'formatted_address'],
        }
      );

      listener = google.maps.event.addListener(
        autocompleteRef.current,
        'place_changed',
        () => {
          const place = autocompleteRef.current?.getPlace();
          if (!place?.address_components) return;

          const addressComponents = place.address_components;
          const formattedAddress = place.formatted_address || '';

          let street = '';
          let city = '';
          let state = '';
          let zipCode = '';

          for (const component of addressComponents) {
            const componentType = component.types[0];

            switch (componentType) {
              case 'street_number':
                street = `${component.long_name} `;
                break;
              case 'route':
                street += component.long_name;
                break;
              case 'locality':
                city = component.long_name;
                break;
              case 'administrative_area_level_1':
                state = component.short_name;
                break;
              case 'postal_code':
                zipCode = component.long_name;
                break;
            }
          }

          onAddressSelected({
            street,
            city,
            state,
            zipCode,
            formattedAddress,
          });
        }
      );
    } catch (error) {
      console.error('Error initializing Google Maps Autocomplete:', error);
    }

    return () => {
      if (listener) {
        google.maps.event.removeListener(listener);
      }
      if (autocompleteRef.current) {
        google.maps.event.clearInstanceListeners(autocompleteRef.current);
      }
    };
  }, [isApiLoaded, onAddressSelected]);

  if (!apiKey) {
    return (
      <div className='space-y-2'>
        <Input
          placeholder='Enter your Google Maps API key'
          onChange={e => setApiKey(e.target.value)}
          className='w-full'
        />
        <p className='text-xs text-gray-500'>
          For testing purposes. In production, this would be stored securely.
        </p>
      </div>
    );
  }

  return (
    <Input
      ref={inputRef}
      placeholder={placeholder}
      className='w-full pl-10'
      disabled={!isApiLoaded}
    />
  );
};

export default AddressAutocomplete;
