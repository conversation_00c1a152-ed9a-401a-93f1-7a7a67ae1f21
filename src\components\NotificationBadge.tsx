import React from 'react';

interface NotificationBadgeProps {
  count: number;
  className?: string;
}

const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  count,
  className = '',
}) => {
  if (count === 0) return null;

  const displayCount = count > 99 ? '99+' : count.toString();

  return (
    <div
      className={`absolute -top-2 -right-[10px] bg-gradient-to-br from-[#FF6F61] to-[#E94E77] text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center font-semibold z-10 ${className}`}
    >
      {displayCount}
    </div>
  );
};

export default NotificationBadge;
