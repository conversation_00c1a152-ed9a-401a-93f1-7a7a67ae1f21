# Nurserv Customer Web Application

## Project Overview

This is a React-based web application for the Nurserv customer platform, providing healthcare services and nurse booking functionality.

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager

### Installation

1. Clone the repository:

```sh
git clone <repository-url>
cd nurserv-c-web
```

2. Install dependencies:

```sh
npm install
```

3. Start the development server:

```sh
npm run dev
```

The application will be available at `http://localhost:3004`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:dev` - Build for development
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier
- `npm run format:check` - Check code formatting

## Technology Stack

- **Frontend Framework**: React 18
- **Build Tool**: Vite
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui, Radix UI
- **State Management**: Redux Toolkit
- **Routing**: React Router
- **Forms**: React Hook Form
- **HTTP Client**: RTK Query
- **Maps**: Google Maps API
- **Charts**: Recharts
- **Animations**: Framer Motion

## Project Structure

```
src/
├── components/     # Reusable UI components
├── pages/         # Page components
├── hooks/         # Custom React hooks
├── store/         # Redux store and API slices
├── types/         # TypeScript type definitions
├── utils/         # Utility functions
└── lib/           # Library configurations
```

## Development Guidelines

- Follow TypeScript best practices
- Use ESLint and Prettier for code formatting
- Write meaningful commit messages
- Test your changes before committing

## Deployment

Build the project for production:

```sh
npm run build
```

The built files will be in the `dist` directory, ready for deployment to any static hosting service.
