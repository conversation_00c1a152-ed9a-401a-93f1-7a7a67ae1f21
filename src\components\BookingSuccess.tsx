import React, { useEffect } from 'react';
import { CheckCircle } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useGetProfileDetailsQuery } from '@/store/api/apiSlice';
import { createNavigationStateFromParams } from '@/utils/navigationState';
import { handleApiError } from '@/utils/toast';

const BookingSuccess = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const booking = location.state?.booking;
  const bookingDetails = location.state?.bookingDetails;

  const {
    data: profileDetails,
    isLoading: profileLoading,
    error: profileError,
  } = useGetProfileDetailsQuery();

  useEffect(() => {
    if (profileError) {
      console.error('Profile details fetch error:', profileError);

      const errorMessage = profileError?.data?.error || profileError?.message;
      const statusCode = profileError?.status;

      handleApiError(profileError, {
        401: 'Session expired. Please log in again.',
        403: 'Access denied. Please check your permissions.',
        404: 'Profile details not found. Please try refreshing.',
        [statusCode]:
          errorMessage || 'Failed to load profile details. Please try again.',
      });
    }
  }, [profileError]);

  const bookingId =
    booking?.booking_id || booking?.id || 'Something went wrong';
  const bookedDate = bookingDetails?.booked_date;
  const timeSlots = bookingDetails?.selectedSlots || [];
  const timeSlot =
    timeSlots.length > 0 ? timeSlots.join(', ') : 'Not specified';
  const nurseName = bookingDetails?.nurse_given_name || 'Not found';
  const hourlyFare = bookingDetails?.hourly_fare || 'Null';

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch {
      return dateString;
    }
  };

  const handleClose = () => {
    if (profileLoading) {
      return;
    }

    const storedEmail = localStorage.getItem('userEmail');

    let navigationState;

    if (profileDetails?.details) {
      navigationState = createNavigationStateFromParams(
        profileDetails.details.email,
        profileDetails.details.given_name,
        profileDetails.details.address,
        profileDetails.details.user_id
      );
    } else if (storedEmail) {
      navigationState = createNavigationStateFromParams(
        storedEmail,
        bookingDetails?.customer_given_name || 'User',
        bookingDetails?.customer_booked_location_address || '',
        bookingDetails?.customer_cognitoId
      );
    } else if (profileDetails?.details?.phone_number) {
      navigationState = createNavigationStateFromParams(
        profileDetails.details.phone_number,
        profileDetails.details.given_name ||
          bookingDetails?.customer_given_name ||
          'User',
        profileDetails.details.address ||
          bookingDetails?.customer_booked_location_address ||
          '',
        profileDetails.details.user_id || bookingDetails?.customer_cognitoId
      );
    } else if (bookingDetails?.customer_given_name) {
      navigationState = createNavigationStateFromParams(
        bookingDetails.customer_given_name,
        bookingDetails.customer_given_name,
        bookingDetails.customer_booked_location_address || '',
        bookingDetails.customer_cognitoId
      );
    }

    if (navigationState) {
      navigate('/home', { state: navigationState });
    } else {
      navigate('/home');
    }
  };

  return (
    <div className='min-h-screen bg-white flex flex-col items-center justify-center text-center p-5'>
      <div className='flex flex-col items-center'>
        {}
        <div className='bg-green-500 rounded-full p-4 mb-2'>
          <CheckCircle className='h-10 w-10 text-white' />
        </div>

        {}
        <h2 className='text-xl font-bold text-center mb-4 text-nursery-darkBlue'>
          Booking Request Sent
        </h2>

        {}
        <p className='text-center text-gray-700 mb-5 leading-relaxed max-w-md'>
          Your request for booking with <strong>{nurseName}</strong> has been
          placed successfully. We will confirm once the nurse accepts your
          booking.
        </p>

        {}
        <div className='w-full max-w-md rounded-lg p-4 mb-6 space-y-3 bg-[#F2F2F2] shadow-sm border'>
          <div className='flex justify-between items-center text-sm'>
            <span className='text-gray-600 font-medium'>Booking ID:</span>
            <span className='text-gray-900 font-semibold'>#{bookingId}</span>
          </div>

          <div className='flex justify-between items-center text-sm'>
            <span className='text-gray-600 font-medium'>Date:</span>
            <span className='text-gray-900 font-semibold'>
              {formatDate(bookedDate)}
            </span>
          </div>

          <div className='flex justify-between items-center text-sm'>
            <span className='text-gray-600 font-medium'>Time Slots:</span>
            <span className='text-gray-900 font-semibold'>{timeSlot}</span>
          </div>

          <div className='flex justify-between items-center text-sm'>
            <span className='text-gray-600 font-medium'>Hourly Rate:</span>
            <span className='text-gray-900 font-semibold'>
              ₹{hourlyFare}/hour
            </span>
          </div>
        </div>

        {}
        <button
          onClick={handleClose}
          disabled={profileLoading}
          className='w-full max-w-md h-10 px-4 py-2 items-center font-semibold border-1 shadow-lg text-white rounded-lg bg-nursery-blue hover:bg-nursery-darkBlue hover:text-white transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed'
        >
          {profileLoading ? 'Loading...' : 'Close and Next'}
        </button>
      </div>
    </div>
  );
};

export default BookingSuccess;
