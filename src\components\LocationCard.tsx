import type React from 'react';
import { useState, useCallback, useEffect, useRef } from 'react';
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { showErrorToast } from '@/utils/toast';
import { AlertCircle, MapPin, Search, Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';

const MAP_CONTAINER_STYLE = {
  width: '100%',
  height: '400px',
} as const;

const DEFAULT_CENTER = {
  lat: 20.5937,
  lng: 78.9629,
} as const;

const LIBRARIES: 'places'[] = ['places'];

const GEOLOCATION_OPTIONS = {
  enableHighAccuracy: true,
  timeout: 10000,
  maximumAge: 300000,
} as const;

const TOAST_DURATION = 3000;
const SEARCH_DELAY = 800;
const MAP_ZOOM_LEVEL = 15;

const ERROR_MESSAGES = {
  PERMISSION_DENIED:
    'Location access was denied. Please enable location services in your browser settings or use the search box.',
  POSITION_UNAVAILABLE:
    'Location information is unavailable. Please use the search box to find your location.',
  TIMEOUT:
    'The request to get your location timed out. Please use the search box to find your location.',
  LOCATION_NOT_FOUND: 'Location not found. Please try a different search.',
  SELECT_LOCATION: 'Please select a location on the map',
  GEOCODING_FAILED: 'Failed to get address for this location',
  NETWORK_LOCATION_FAILED: 'Error fetching network location',
} as const;

interface LocationData {
  latitude: number;
  longitude: number;
  address: string;
}

interface Position {
  lat: number;
  lng: number;
}

interface NetworkLocationResponse {
  latitude: number;
  longitude: number;
}

const getNetworkLocation = async (): Promise<Position> => {
  try {
    const response = await fetch('https://ipapi.co/json/', {
      signal: AbortSignal.timeout(5000),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: NetworkLocationResponse = await response.json();

    if (
      typeof data.latitude !== 'number' ||
      typeof data.longitude !== 'number'
    ) {
      throw new Error('Invalid location data received');
    }

    return {
      lat: data.latitude,
      lng: data.longitude,
    };
  } catch (error) {
    console.error(ERROR_MESSAGES.NETWORK_LOCATION_FAILED, error);

    return DEFAULT_CENTER;
  }
};

interface LocationCardProps {
  onLocationSelect: (locationData: LocationData) => void;
  isLoading?: boolean;
  buttonText?: string;
  title?: string;
  showSearchHint?: boolean;
  initialLocation?: {
    lat: number;
    lng: number;
    address?: string;
  };
  enableGeolocation?: boolean;
  geolocationPurpose?: string;
  requireUserConsent?: boolean;
}

const LocationCard: React.FC<LocationCardProps> = ({
  onLocationSelect,
  isLoading = false,
  buttonText = 'Confirm Location',
  title = 'Select Your Location',
  showSearchHint = true,
  initialLocation,
  enableGeolocation = false,
  geolocationPurpose = 'to provide location-based services',
  requireUserConsent = true,
}) => {
  const [marker, setMarker] = useState<Position>(
    initialLocation || DEFAULT_CENTER
  );
  const [address, setAddress] = useState<string>(
    initialLocation?.address || ''
  );
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [geoError, setGeoError] = useState<string | null>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState<boolean>(true);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [isMapLoaded, setIsMapLoaded] = useState<boolean>(false);
  const [searchSuggestions, setSearchSuggestions] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);
  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);
  const [showConsentDialog, setShowConsentDialog] = useState<boolean>(false);
  const [hasUserConsented, setHasUserConsented] = useState<boolean>(false);

  const searchInputRef = useRef<HTMLInputElement>(null);
  const mapRef = useRef<google.maps.Map | null>(null);
  const geocoderRef = useRef<google.maps.Geocoder | null>(null);
  const autocompleteServiceRef =
    useRef<google.maps.places.AutocompleteService | null>(null);
  const placesServiceRef = useRef<google.maps.places.PlacesService | null>(
    null
  );
  const confirmButtonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (isMapLoaded && window.google) {
      geocoderRef.current = new window.google.maps.Geocoder();
      autocompleteServiceRef.current =
        new window.google.maps.places.AutocompleteService();
      if (mapRef.current) {
        placesServiceRef.current = new window.google.maps.places.PlacesService(
          mapRef.current
        );
      }
    }
  }, [isMapLoaded]);

  useEffect(() => {
    if (searchQuery && autocompleteServiceRef.current) {
      setIsSearching(true);
      autocompleteServiceRef.current.getPlacePredictions(
        {
          input: searchQuery,
          componentRestrictions: { country: 'in' },
        },
        (predictions, status) => {
          setIsSearching(false);
          if (
            status === window.google.maps.places.PlacesServiceStatus.OK &&
            predictions
          ) {
            setSearchSuggestions(predictions);
            setShowSuggestions(true);
          } else {
            setSearchSuggestions([]);
          }
        }
      );
    } else {
      setSearchSuggestions([]);
      setShowSuggestions(false);
    }
  }, [searchQuery]);

  const reverseGeocode = useCallback(
    (position: Position) => {
      if (!geocoderRef.current) {
        geocoderRef.current = new google.maps.Geocoder();
      }

      geocoderRef.current.geocode({ location: position }, (results, status) => {
        if (status === 'OK' && results?.[0]) {
          setAddress(results[0].formatted_address);
        } else {
          console.error('Geocoding failed:', status);
          setAddress(ERROR_MESSAGES.GEOCODING_FAILED);
        }
      });
    },
    [setAddress]
  );

  const requestGeolocationWithConsent = useCallback(() => {
    if (!('geolocation' in navigator)) {
      setGeoError('Geolocation is not supported by this browser.');
      setIsLoadingLocation(false);
      return;
    }

    if (!hasUserConsented) {
      setShowConsentDialog(true);
      return;
    }

    setGeoError(null);

    const successHandler = (position: GeolocationPosition) => {
      const pos: Position = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      };
      setMarker(pos);
      reverseGeocode(pos);
      setIsLoadingLocation(false);
    };

    const errorHandler = (error: GeolocationPositionError) => {
      let errorMessage: string;

      switch (error.code) {
        case error.PERMISSION_DENIED:
          errorMessage = ERROR_MESSAGES.PERMISSION_DENIED;
          break;
        case error.POSITION_UNAVAILABLE:
          errorMessage = ERROR_MESSAGES.POSITION_UNAVAILABLE;
          break;
        case error.TIMEOUT:
          errorMessage = ERROR_MESSAGES.TIMEOUT;
          break;
        default:
          errorMessage = 'Unable to retrieve your location.';
      }

      setGeoError(errorMessage);
      setIsLoadingLocation(false);

      if (error.code !== error.TIMEOUT) {
        showErrorToast(errorMessage, { duration: TOAST_DURATION });
      }
    };

    navigator.geolocation.getCurrentPosition(
      successHandler,
      errorHandler,
      GEOLOCATION_OPTIONS
    );
  }, [reverseGeocode, hasUserConsented]);

  const requestGeolocationDirect = useCallback(() => {
    if (!('geolocation' in navigator)) {
      setGeoError('Geolocation is not supported by this browser.');
      setIsLoadingLocation(false);
      return;
    }

    setGeoError(null);

    const successHandler = (position: GeolocationPosition) => {
      const newPosition: Position = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      };
      setMarker(newPosition);
      reverseGeocode(newPosition);
      setIsLoadingLocation(false);
    };

    const errorHandler = (error: GeolocationPositionError) => {
      setIsLoadingLocation(false);
      let errorMessage = 'Unable to retrieve your location.';

      switch (error.code) {
        case error.PERMISSION_DENIED:
          errorMessage = 'Location access denied by user.';
          break;
        case error.POSITION_UNAVAILABLE:
          errorMessage = 'Location information is unavailable.';
          break;
        case error.TIMEOUT:
          errorMessage = 'Location request timed out.';
          break;
      }

      setGeoError(errorMessage);
      if (error.code !== error.TIMEOUT) {
        showErrorToast(errorMessage, { duration: TOAST_DURATION });
      }
    };

    navigator.geolocation.getCurrentPosition(
      successHandler,
      errorHandler,
      GEOLOCATION_OPTIONS
    );
  }, [reverseGeocode]);

  const handleGeolocationConsent = useCallback(
    (consent: boolean) => {
      setShowConsentDialog(false);
      setHasUserConsented(consent);

      if (consent) {
        requestGeolocationDirect();
      }
    },
    [requestGeolocationDirect]
  );

  const handleGeolocationRequest = useCallback(() => {
    if (requireUserConsent && !hasUserConsented) {
      requestGeolocationWithConsent();
    } else {
      requestGeolocationDirect();
    }
  }, [
    requireUserConsent,
    hasUserConsented,
    requestGeolocationWithConsent,
    requestGeolocationDirect,
  ]);

  useEffect(() => {
    if (!initialLocation) {
      const initializeLocation = async () => {
        try {
          setIsLoadingLocation(true);

          const networkLocation = await getNetworkLocation();
          setMarker(networkLocation);
          reverseGeocode(networkLocation);

          if (enableGeolocation && 'geolocation' in navigator) {
            handleGeolocationRequest();
          } else {
            setIsLoadingLocation(false);
          }
        } catch (error) {
          console.error('Error initializing location:', error);
          setIsLoadingLocation(false);
        }
      };

      initializeLocation();
    } else {
      setIsLoadingLocation(false);
    }
  }, [
    initialLocation,
    enableGeolocation,
    hasUserConsented,
    handleGeolocationRequest,
    requireUserConsent,
    reverseGeocode,
  ]);

  const scrollToConfirmButton = useCallback(() => {
    if (confirmButtonRef.current) {
      confirmButtonRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }, []);

  const handleMapClick = useCallback(
    (e: google.maps.MapMouseEvent) => {
      if (e.latLng) {
        const newPosition: Position = {
          lat: e.latLng.lat(),
          lng: e.latLng.lng(),
        };
        setMarker(newPosition);
        reverseGeocode(newPosition);
      }
    },
    [reverseGeocode]
  );

  const handleMapLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
    setIsMapLoaded(true);
    geocoderRef.current = new google.maps.Geocoder();
    placesServiceRef.current = new google.maps.places.PlacesService(map);
  }, []);

  const handleMarkerDragEnd = useCallback(
    (e: google.maps.MapMouseEvent) => {
      if (e.latLng) {
        const newPosition: Position = {
          lat: e.latLng.lat(),
          lng: e.latLng.lng(),
        };
        setMarker(newPosition);
        reverseGeocode(newPosition);
      }
    },
    [reverseGeocode]
  );

  const handleSubmit = useCallback(() => {
    if (!address.trim()) {
      showErrorToast(ERROR_MESSAGES.SELECT_LOCATION, {
        duration: TOAST_DURATION,
      });
      return;
    }

    onLocationSelect({
      latitude: marker.lat,
      longitude: marker.lng,
      address: address.trim(),
    });
  }, [address, marker, onLocationSelect]);

  const handleSuggestionClick = useCallback(
    (placeId: string) => {
      if (!placesServiceRef.current) return;

      placesServiceRef.current.getDetails(
        {
          placeId,
          fields: ['geometry', 'formatted_address', 'name'],
        },
        (place, status) => {
          if (
            status === window.google.maps.places.PlacesServiceStatus.OK &&
            place?.geometry?.location
          ) {
            const newPosition: Position = {
              lat: place.geometry.location.lat(),
              lng: place.geometry.location.lng(),
            };

            setMarker(newPosition);
            setAddress(place.formatted_address || '');
            setSearchQuery('');
            setShowSuggestions(false);

            if (mapRef.current) {
              mapRef.current.panTo(newPosition);
              mapRef.current.setZoom(MAP_ZOOM_LEVEL);
            }

            setTimeout(scrollToConfirmButton, SEARCH_DELAY);
          }
        }
      );
    },
    [scrollToConfirmButton]
  );

  const handleSearch = useCallback(() => {
    if (!searchQuery.trim() || !geocoderRef.current) return;

    setIsSearching(true);
    geocoderRef.current.geocode(
      { address: searchQuery.trim() },
      (results, status) => {
        setIsSearching(false);
        if (status === 'OK' && results?.[0]) {
          const location = results[0].geometry.location;
          const newPosition: Position = {
            lat: location.lat(),
            lng: location.lng(),
          };

          setMarker(newPosition);
          setAddress(results[0].formatted_address);
          setShowSuggestions(false);

          if (mapRef.current) {
            mapRef.current.panTo(newPosition);
            mapRef.current.setZoom(MAP_ZOOM_LEVEL);
          }

          setTimeout(scrollToConfirmButton, SEARCH_DELAY);
        } else {
          showErrorToast(ERROR_MESSAGES.LOCATION_NOT_FOUND, {
            duration: TOAST_DURATION,
          });
        }
      }
    );
  }, [searchQuery, scrollToConfirmButton]);

  return (
    <Card className='w-full max-w-2xl bg-white rounded-md p-6 shadow-lg'>
      <h2 className='text-2xl font-bold text-center mb-6 text-gray-800'>
        {title}
      </h2>

      {geoError && (
        <Alert variant='destructive' className='mb-4'>
          <AlertCircle className='h-4 w-4' />
          <AlertTitle>Location Error</AlertTitle>
          <AlertDescription>{geoError}</AlertDescription>
        </Alert>
      )}

      {}
      {showConsentDialog && (
        <Alert className='mb-4 border-blue-200 bg-blue-50'>
          <MapPin className='h-4 w-4 text-blue-600' />
          <AlertTitle className='text-blue-800'>
            Location Access Request
          </AlertTitle>
          <AlertDescription className='text-blue-700 mb-3'>
            This application would like to access your current location{' '}
            {geolocationPurpose}. Your location data will only be used for this
            purpose and will not be stored or shared.
          </AlertDescription>
          <div className='flex gap-2'>
            <Button
              size='sm'
              onClick={() => handleGeolocationConsent(true)}
              className='bg-blue-600 hover:bg-blue-700 text-white'
            >
              Allow Location Access
            </Button>
            <Button
              size='sm'
              variant='outline'
              onClick={() => handleGeolocationConsent(false)}
              className='border-blue-300 text-blue-700 hover:bg-blue-100'
            >
              Use Search Instead
            </Button>
          </div>
        </Alert>
      )}

      <div className='space-y-6'>
        {}
        <div className='relative'>
          <div className='relative'>
            <Input
              ref={searchInputRef}
              type='text'
              placeholder='Search for a location or enter an address'
              className='w-full pr-12 h-10 text-base border-2 border-gray-300 focus:border-200 transition-all duration-200'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
            />
            <Button
              type='button'
              size='icon'
              variant='ghost'
              className='absolute right-1 top-1 h-10 w-10 hover:bg-gray-100 transition-colors duration-200'
              disabled={isSearching}
              onClick={handleSearch}
            >
              {isSearching ? (
                <Loader2 className='h-5 w-5 animate-spin text-gray-600' />
              ) : (
                <Search className='h-5 w-5 text-gray-600' />
              )}
            </Button>
          </div>

          {}
          {showSuggestions && searchSuggestions.length > 0 && (
            <div className='absolute z-50 w-full mt-2 bg-white border-2 border-gray-200 rounded-lg shadow-xl max-h-64 overflow-y-auto'>
              {searchSuggestions.map((suggestion, index) => (
                <button
                  key={suggestion.place_id}
                  type='button'
                  className={`w-full px-4 py-3 hover:bg-gray-50 focus:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset cursor-pointer flex items-start transition-colors duration-150 text-left ${
                    index !== searchSuggestions.length - 1
                      ? 'border-b border-gray-100'
                      : ''
                  }`}
                  onClick={() => handleSuggestionClick(suggestion.place_id)}
                  aria-label={`Select location: ${suggestion.structured_formatting?.main_text}, ${suggestion.structured_formatting?.secondary_text}`}
                >
                  <MapPin className='h-5 w-5 mt-0.5 mr-3 flex-shrink-0 text-gray-500' />
                  <div className='flex-1 min-w-0'>
                    <div className='text-sm font-medium text-gray-900 truncate'>
                      {suggestion.structured_formatting?.main_text}
                    </div>
                    <div className='text-xs text-gray-500 mt-1 truncate'>
                      {suggestion.structured_formatting?.secondary_text}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {geoError && showSearchHint && (
            <div className='text-sm text-gray-600 mt-2 bg-blue-50 p-3 rounded-md border border-blue-200'>
              <p className='mb-2'>
                💡 Tip: Enter your address in the search box above to find your
                location.
              </p>
              {!enableGeolocation && (
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={handleGeolocationRequest}
                  disabled={isLoadingLocation}
                  className='mt-2'
                >
                  {isLoadingLocation ? (
                    <>
                      <Loader2 className='h-4 w-4 animate-spin mr-2' />
                      Getting location...
                    </>
                  ) : (
                    <>
                      <MapPin className='h-4 w-4 mr-2' />
                      Use my current location
                    </>
                  )}
                </Button>
              )}
            </div>
          )}
        </div>

        {}
        <div className='relative border-2 border-gray-200 rounded-lg overflow-hidden'>
          {!isMapLoaded && (
            <Skeleton className='w-full h-[400px] absolute top-0 left-0 z-10 bg-gray-200' />
          )}
          <LoadScript
            googleMapsApiKey={import.meta.env.VITE_GOOGLE_MAPS_API_KEY}
            libraries={LIBRARIES}
          >
            <GoogleMap
              mapContainerStyle={MAP_CONTAINER_STYLE}
              center={marker}
              zoom={MAP_ZOOM_LEVEL}
              onClick={handleMapClick}
              onLoad={handleMapLoad}
              options={{
                zoomControl: true,
                mapTypeControl: true,
                scaleControl: true,
                streetViewControl: true,
                rotateControl: true,
                fullscreenControl: true,
                styles: [
                  {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [{ visibility: 'on' }],
                  },
                ],
              }}
            >
              <Marker
                position={marker}
                draggable={true}
                onDragEnd={handleMarkerDragEnd}
              />
            </GoogleMap>
          </LoadScript>
        </div>

        {}
        <div className='space-y-2'>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Selected Address
          </label>
          <Input
            type='text'
            value={address}
            readOnly
            placeholder='Click on the map or search to select location'
            className='w-full h-12 text-base bg-gray-50 border-2 border-gray-200 text-gray-800 font-medium'
          />
        </div>

        {}
        <Button
          ref={confirmButtonRef}
          onClick={handleSubmit}
          className='w-full h-12 bg-[#4AB4CE] hover:bg-[#3a9bb3] text-white font-medium text-base transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed'
          disabled={isLoading || !address}
        >
          {isLoading ? (
            <>
              <Loader2 className='h-5 w-5 animate-spin mr-2' />
              Updating...
            </>
          ) : (
            buttonText
          )}
        </Button>
      </div>
    </Card>
  );
};

export default LocationCard;
