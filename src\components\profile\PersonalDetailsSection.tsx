import React, { useEffect, useRef } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  useUpdateCustomerPersonalDetailsMutation,
  useGetProfileDetailsQuery,
} from '../../store/api/apiSlice';
import { showErrorToast, showSuccessToast } from '@/utils/toast';

interface PersonalDetailsSectionProps {
  expanded: boolean;
  setExpanded: React.Dispatch<React.SetStateAction<boolean>>;

  given_name: string;
  setGiven_name: React.Dispatch<React.SetStateAction<string>>;

  family_name: string;
  setFamily_name: React.Dispatch<React.SetStateAction<string>>;

  email: string;
  setEmail: React.Dispatch<React.SetStateAction<string>>;

  isDisabled?: boolean;
}

const PersonalDetailsSection = ({
  expanded,
  setExpanded,
  given_name,
  setGiven_name,
  family_name,
  setFamily_name,
  email,
  setEmail,
  isDisabled = false,
}: PersonalDetailsSectionProps) => {
  const { data: currentDetails, refetch } = useGetProfileDetailsQuery(
    undefined,
    {
      skip: false,
    }
  );

  useEffect(() => {
    if (currentDetails) {
      setGiven_name(currentDetails?.details?.given_name || '');

      setFamily_name(currentDetails?.details?.family_name || '');

      setEmail(currentDetails?.details?.email || '');

      initialValuesRef.current = {
        given_name: currentDetails?.details?.given_name || '',
        family_name: currentDetails?.details?.family_name || '',
        email: currentDetails?.details?.email || '',
      };
    }
  }, [currentDetails, setGiven_name, setFamily_name, setEmail]);

  const initialValuesRef = useRef({
    given_name: given_name || '',
    family_name: family_name || '',
    email: email || '',
  });

  const [
    updatePersonalDetails,
    { isLoading: _isLoading, isSuccess: _isSuccess, isError, error },
  ] = useUpdateCustomerPersonalDetailsMutation();

  useEffect(() => {
    if (isError) {
      showErrorToast('Failed to update details. Please try again.');
      console.error('Update Error details:', error);
    }
  }, [isError, error]);

  const handleUpdateProfile = async () => {
    try {
      const personalDetails = {
        given_name: given_name,
        family_name: family_name,
        email: email,
      };

      const result = await updatePersonalDetails(personalDetails).unwrap();

      if (result.message?.includes('updated successfully') || result.details) {
        showSuccessToast('Personal Details Updated Successfully');

        initialValuesRef.current = {
          given_name: given_name || '',
          family_name: family_name || '',
          email: email || '',
        };

        await refetch();
      } else {
        showErrorToast(result.message || 'Update Failed. Please try again.');
      }
    } catch (err) {
      console.error('Failed to update profile:', err);
      showErrorToast(
        'Update failed. Please check your connection and try again.'
      );
    }
  };
  return (
    <div className='bg-[#F2F2F2] rounded-lg p-5 shadow-lg'>
      <Collapsible
        open={expanded}
        onOpenChange={setExpanded}
        className='w-full'
      >
        <CollapsibleTrigger asChild className='w-full'>
          <div className='flex justify-between items-center cursor-pointer py-2'>
            <h2 className='text-xl font-bold'>Personal Details</h2>
            {expanded ? (
              <ChevronUp className='h-6 w-6 text-gray-400' />
            ) : (
              <ChevronDown className='h-6 w-6 text-gray-400' />
            )}
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent className='space-y-4 mt-2'>
          <fieldset
            disabled={isDisabled}
            className={cn('space-y-4', {
              'opacity-95 pointer-events-none': isDisabled,
            })}
          >
            <div>
              <label className='text-gray-800 text-sm'>First Name *</label>
              <Input
                type='text'
                placeholder='Enter You First Name'
                className='mt-1'
                value={given_name}
                onChange={e => setGiven_name(e.target.value)}
              />
            </div>

            <div>
              <label className='text-gray-800 text-sm'>Last Name *</label>
              <Input
                type='text'
                className='mt-1'
                value={family_name}
                placeholder='Enter Your Last Name'
                onChange={e => setFamily_name(e.target.value)}
              />
            </div>

            <div>
              <label className='text-gray-800 text-sm'>Enter Your Email</label>
              <Input
                type='email'
                placeholder='Enter Your Email here'
                value={email}
                className='mt-1'
                onChange={e => setEmail(e.target.value)}
              />
            </div>
            <Button
              className='bg-nursery-darkBlue hover:bg-[#1e6880] text-white hover:shadow-xl transition-colors duration-300 ease-in-out'
              onClick={handleUpdateProfile}
            >
              Update Profile
            </Button>
          </fieldset>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default PersonalDetailsSection;
